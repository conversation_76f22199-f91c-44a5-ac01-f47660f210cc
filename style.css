* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  background: #07273e;
  height: 100vh;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background: #cdff9a;
  backdrop-filter: blur(10px);
  padding: 20px 0;
  position: relative;
  z-index: 100;
  width: 100%;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 20px;
  padding-right: 20px;
}

.logo h2 {
  color: #2c5530;
  font-weight: bold;
  font-size: 24px;
  line-height: 1.2;
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 40px;
}

.nav a {
  text-decoration: none;
  color: #2c5530;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

.nav a.active,
.nav a:hover {
  color: #1a3d1f;
}

.header-icons {
  display: flex;
  gap: 20px;
}

.header-icons i {
  color: #2c5530;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.header-icons i:hover {
  color: #1a3d1f;
}

.main-content {
  /* padding-top: 100px; */
  height: calc(100vh - 80px);
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 60px;
  height: 100%;
}

.left-content {
  flex: 1;
  position: relative;
}

.side-text {
  position: absolute;
  left: -80px;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  transform-origin: center;
}

.side-text span {
  color: #2c5530;
  font-size: 12px;
  letter-spacing: 2px;
  font-weight: 500;
}

.main-text {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-direction: row-reverse;
  justify-content: space-between;
}

.number {
  font-size: 200px;
  font-weight: bold;
  color: rgba(44, 85, 48, 0.3);
  line-height: 1;
}

.text-content {
  flex: 1;
  padding-top: 0;
  text-align: left;
  position: relative;
  top: 50px;
  left: 250px;
  width: 50%;
}

.subtitle {
  color: #2c5530;
  font-size: 12px;
  letter-spacing: 2px;
  font-weight: 500;
  margin-bottom: 20px;
  display: block;
}

.text-content h1 {
  color: #1a3d1f;
  font-size: 48px;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 40px;
}

.cta-button {
  background: transparent;
  border: 2px solid #2c5530;
  color: #2c5530;
  padding: 15px 30px;
  font-size: 12px;
  letter-spacing: 2px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: #2c5530;
  color: white;
}

.social-icons {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.social-icons a {
  color: #2c5530;
  font-size: 18px;
  transition: color 0.3s ease;
}

.social-icons a:hover {
  color: #1a3d1f;
}

/* Right Content */
.right-content {
  flex: 1;
  position: relative;
}

.image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  height: 300px;
}

.image-tag {
  position: absolute;
  top: 20px;
  left: 20px;
  background: #007bff;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
}

.image-container img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.navigation-arrows {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.nav-arrow {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-arrow:hover {
  background: white;
  transform: scale(1.1);
}

.nav-arrow i {
  color: #333;
  font-size: 14px;
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdown-toggle {
  position: relative;
  cursor: pointer;
}

.dropdown-menu {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 0;
  margin: 0;
  width: 100%;
  display: none;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.dropdown-menu.show {
  display: block;
  opacity: 1;
  visibility: visible;
}

.dropdown-content {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.dropdown-categories {
  flex: 1;
  background: #f8f9fa;
  padding: 20px;
  border-right: 1px solid #e9ecef;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  border: 1px solid #e9ecef;
}

.category-item:hover {
  background: #cdff9a;
  border-color: #2c5530;
  transform: translateX(5px);
}

.category-title {
  font-weight: 500;
  color: #2c5530;
  font-size: 14px;
}

.category-item i {
  color: #6c757d;
  font-size: 12px;
  transition: color 0.3s ease;
}

.category-item:hover i {
  color: #2c5530;
}

.dropdown-products {
  flex: 1.5;
  padding: 20px;
  background: white;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  align-content: start;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.product-item:hover {
  background: #f8f9fa;
  border-color: #cdff9a;
  transform: translateY(-2px);
}

.product-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.product-item:hover img {
  transform: scale(1.05);
}

.product-item span {
  font-size: 13px;
  color: #2c5530;
  font-weight: 500;
  line-height: 1.3;
}
.content-main {
  display: flex;
}

.color-icon {
  color: #ffffff;
}
.vertical-text {
  transform: rotate(-90deg);
  transform-origin: left top;
  display: inline-block;
  font-size: 20px;
  font-weight: bold;
}
/* Responsive Design */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    gap: 40px;
  }

  .side-text {
    display: none;
  }

  .main-text {
    flex-direction: column;
    gap: 20px;
  }

  .number {
    font-size: 80px;
  }

  .text-content h1 {
    font-size: 32px;
  }

  .nav ul {
    gap: 20px;
  }

  .header .container {
    flex-wrap: wrap;
    gap: 20px;
  }

  .dropdown-menu {
    top: 70px;
  }

  .dropdown-content {
    padding: 0 10px;
  }

  .dropdown-content {
    flex-direction: column;
  }

  .dropdown-categories {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .dropdown-products {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
}
